# CLAUDE.md

This file contains project-specific context and instructions for <PERSON>.

## Project Overview
Python project for ticket purchasing automation on Damai.com

## Testing Commands
- Run tests: `poetry run test` or `poetry run tests`
- Run with coverage: `poetry run pytest --cov`

## Development Guidelines
- Use Poetry for dependency management
- Follow pytest conventions for test organization
- Maintain 80% test coverage threshold